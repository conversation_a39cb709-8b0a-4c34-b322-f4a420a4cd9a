use crate::entity_system::{EntitySystem, EntityType as SystemEntityType};
use crate::components::basic_info::BasicInfo;
use crate::Position;
use std::sync::Arc;
use std::sync::atomic::{AtomicU64, Ordering};
use tonic::{Request, Response, Status};
use tracing::{debug, error, info, warn};
use dashmap::DashMap;
use once_cell::sync::Lazy;

pub mod entity_management {
    tonic::include_proto!("entity_management");
}

use entity_management::entity_management_service_server::EntityManagementService;
use entity_management::*;

// Global entity ID generator using AtomicU64
static ENTITY_ID_COUNTER: AtomicU64 = AtomicU64::new(1);

// Global map to track client -> entity mappings per map
static CLIENT_ENTITY_MAP: Lazy<DashMap<(u32, String), u64>> = Lazy::new(|| DashMap::new());

pub struct MyEntityManagementService {
    pub map_id: u32,
    pub entity_system: Arc<EntitySystem>,
}

impl MyEntityManagementService {
    pub fn new(map_id: u32, entity_system: Arc<EntitySystem>) -> Self {
        Self {
            map_id,
            entity_system,
        }
    }

    fn generate_entity_id() -> u64 {
        ENTITY_ID_COUNTER.fetch_add(1, Ordering::SeqCst)
    }

    fn convert_entity_type(proto_type: i32) -> SystemEntityType {
        match EntityType::try_from(proto_type).unwrap_or(EntityType::Unspecified) {
            EntityType::Player => SystemEntityType::Player,
            EntityType::Npc => SystemEntityType::Npc,
            EntityType::Mob => SystemEntityType::Mob,
            _ => SystemEntityType::Player, // Default fallback (no Item type in system)
        }
    }

    fn convert_to_proto_entity_type(system_type: SystemEntityType) -> EntityType {
        match system_type {
            SystemEntityType::Player => EntityType::EntityTypePlayer,
            SystemEntityType::Npc => EntityType::EntityTypeNpc,
            SystemEntityType::Mob => EntityType::EntityTypeMob,
        }
    }
}

#[tonic::async_trait]
impl EntityManagementService for MyEntityManagementService {
    async fn assign_entity(
        &self,
        request: Request<AssignEntityRequest>,
    ) -> Result<Response<AssignEntityResponse>, Status> {
        let req = request.into_inner();
        
        debug!("Assigning entity for client: {} on map: {}", req.client_id, req.map_id);
        
        if req.map_id != self.map_id {
            return Ok(Response::new(AssignEntityResponse {
                success: false,
                entity_id: 0,
                message: format!("Map ID mismatch: expected {}, got {}", self.map_id, req.map_id),
            }));
        }

        let entity_id = Self::generate_entity_id();
        let entity_type = Self::convert_entity_type(req.entity_type);
        
        // Create entity in the ECS system
        let position = Position {
            x: req.entity_data.as_ref().map(|d| d.x).unwrap_or(0.0),
            y: req.entity_data.as_ref().map(|d| d.y).unwrap_or(0.0),
            z: req.entity_data.as_ref().map(|d| d.z).unwrap_or(0.0),
        };
        
        let basic_info = BasicInfo {
            id: entity_id as u16, // Convert to u16 for compatibility
            name: req.entity_data.as_ref().map(|d| d.name.clone()).unwrap_or_default(),
            entity_type,
        };

        // Spawn entity in the system
        self.entity_system.entity_factory.lock().unwrap().world.spawn((basic_info, position));
        
        // Store client -> entity mapping
        CLIENT_ENTITY_MAP.insert((req.map_id, req.client_id.clone()), entity_id);
        
        info!("Successfully assigned entity_id: {} to client: {} on map: {}", entity_id, req.client_id, req.map_id);
        
        Ok(Response::new(AssignEntityResponse {
            success: true,
            entity_id,
            message: "Entity assigned successfully".to_string(),
        }))
    }

    async fn reset_entity(
        &self,
        request: Request<ResetEntityRequest>,
    ) -> Result<Response<ResetEntityResponse>, Status> {
        let req = request.into_inner();
        
        debug!("Resetting entity for client: {} on map: {}", req.client_id, req.map_id);
        
        if req.map_id != self.map_id {
            return Ok(Response::new(ResetEntityResponse {
                success: false,
                message: format!("Map ID mismatch: expected {}, got {}", self.map_id, req.map_id),
            }));
        }

        // Remove client -> entity mapping
        if let Some((_, entity_id)) = CLIENT_ENTITY_MAP.remove(&(req.map_id, req.client_id.clone())) {
            // TODO: Remove entity from ECS system if needed
            info!("Successfully reset entity_id: {} for client: {} on map: {}", entity_id, req.client_id, req.map_id);
        } else {
            warn!("No entity mapping found for client: {} on map: {}", req.client_id, req.map_id);
        }
        
        Ok(Response::new(ResetEntityResponse {
            success: true,
            message: "Entity reset successfully".to_string(),
        }))
    }

    async fn list_entities(
        &self,
        request: Request<ListEntitiesRequest>,
    ) -> Result<Response<ListEntitiesResponse>, Status> {
        let req = request.into_inner();
        
        debug!("Listing entities on map: {}", req.map_id);
        
        if req.map_id != self.map_id {
            return Ok(Response::new(ListEntitiesResponse {
                entities: vec![],
            }));
        }

        let mut entities = Vec::new();
        
        // Iterate through client mappings for this map
        for entry in CLIENT_ENTITY_MAP.iter() {
            let ((map_id, client_id), entity_id) = (entry.key(), entry.value());
            
            if *map_id == req.map_id {
                // Filter by entity type if specified
                if let Some(filter_type) = req.entity_type {
                    // For now, we'll create a basic entity info
                    // In a real implementation, you'd query the ECS system
                }
                
                entities.push(EntityInfo {
                    entity_id: *entity_id,
                    client_id: client_id.clone(),
                    map_id: *map_id,
                    entity_type: EntityType::EntityTypePlayer as i32, // Default for now
                    entity_data: Some(EntityData {
                        x: 0.0,
                        y: 0.0,
                        z: 0.0,
                        hp: 100,
                        max_hp: 100,
                        name: format!("Entity_{}", entity_id),
                    }),
                    created_at: chrono::Utc::now().timestamp(),
                });
            }
        }
        
        debug!("Found {} entities on map: {}", entities.len(), req.map_id);
        
        Ok(Response::new(ListEntitiesResponse { entities }))
    }

    async fn get_entity(
        &self,
        request: Request<GetEntityRequest>,
    ) -> Result<Response<GetEntityResponse>, Status> {
        let req = request.into_inner();
        
        debug!("Getting entity: {} on map: {}", req.entity_id, req.map_id);
        
        if req.map_id != self.map_id {
            return Ok(Response::new(GetEntityResponse {
                found: false,
                entity: None,
            }));
        }

        // Find the entity in our mappings
        for entry in CLIENT_ENTITY_MAP.iter() {
            let ((map_id, client_id), entity_id) = (entry.key(), entry.value());
            
            if *map_id == req.map_id && *entity_id == req.entity_id {
                let entity_info = EntityInfo {
                    entity_id: *entity_id,
                    client_id: client_id.clone(),
                    map_id: *map_id,
                    entity_type: EntityType::EntityTypePlayer as i32,
                    entity_data: Some(EntityData {
                        x: 0.0,
                        y: 0.0,
                        z: 0.0,
                        hp: 100,
                        max_hp: 100,
                        name: format!("Entity_{}", entity_id),
                    }),
                    created_at: chrono::Utc::now().timestamp(),
                };
                
                return Ok(Response::new(GetEntityResponse {
                    found: true,
                    entity: Some(entity_info),
                }));
            }
        }
        
        Ok(Response::new(GetEntityResponse {
            found: false,
            entity: None,
        }))
    }

    async fn join_map(
        &self,
        request: Request<JoinMapRequest>,
    ) -> Result<Response<JoinMapResponse>, Status> {
        let req = request.into_inner();
        
        debug!("Client {} joining map: {}", req.client_id, req.map_id);
        
        if req.map_id != self.map_id {
            return Ok(Response::new(JoinMapResponse {
                success: false,
                entity_id: 0,
                message: format!("Map ID mismatch: expected {}, got {}", self.map_id, req.map_id),
            }));
        }

        // Check if client already has an entity on this map
        if let Some(existing_entity_id) = CLIENT_ENTITY_MAP.get(&(req.map_id, req.client_id.clone())) {
            warn!("Client {} already has entity {} on map {}", req.client_id, existing_entity_id.value(), req.map_id);
            return Ok(Response::new(JoinMapResponse {
                success: true,
                entity_id: *existing_entity_id.value(),
                message: "Already joined map".to_string(),
            }));
        }

        let entity_id = Self::generate_entity_id();
        
        // Create player entity
        let position = Position {
            x: req.spawn_x,
            y: req.spawn_y,
            z: req.spawn_z,
        };
        
        let basic_info = BasicInfo {
            id: entity_id as u16,
            name: format!("Player_{}", req.client_id),
            entity_type: SystemEntityType::Player,
        };

        // Spawn player in the ECS system
        self.entity_system.entity_factory.lock().unwrap().world.spawn((basic_info, position));
        
        // Store client -> entity mapping
        CLIENT_ENTITY_MAP.insert((req.map_id, req.client_id.clone()), entity_id);
        
        info!("Client {} successfully joined map {} with entity_id: {}", req.client_id, req.map_id, entity_id);
        
        Ok(Response::new(JoinMapResponse {
            success: true,
            entity_id,
            message: "Successfully joined map".to_string(),
        }))
    }

    async fn leave_map(
        &self,
        request: Request<LeaveMapRequest>,
    ) -> Result<Response<LeaveMapResponse>, Status> {
        let req = request.into_inner();
        
        debug!("Client {} leaving map: {}", req.client_id, req.map_id);
        
        if req.map_id != self.map_id {
            return Ok(Response::new(LeaveMapResponse {
                success: false,
                message: format!("Map ID mismatch: expected {}, got {}", self.map_id, req.map_id),
            }));
        }

        // Remove client -> entity mapping
        if let Some((_, entity_id)) = CLIENT_ENTITY_MAP.remove(&(req.map_id, req.client_id.clone())) {
            // TODO: Remove entity from ECS system if needed
            info!("Client {} successfully left map {} (entity_id: {})", req.client_id, req.map_id, entity_id);
        } else {
            warn!("No entity mapping found for client: {} on map: {}", req.client_id, req.map_id);
        }
        
        Ok(Response::new(LeaveMapResponse {
            success: true,
            message: "Successfully left map".to_string(),
        }))
    }
}
