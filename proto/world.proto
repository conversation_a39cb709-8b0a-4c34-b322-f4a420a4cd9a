syntax = "proto3";

package world;

service WorldService {
  rpc <PERSON>haracter(CharacterRequest) returns (CharacterResponse);
  rpc ChangeMap(ChangeMapRequest) returns (ChangeMapResponse);
  rpc MoveCharacter(CharacterMoveRequest) returns (CharacterMoveResponse);
  rpc GetTargetHp(ObjectHpRequest) returns (ObjectHpResponse);
  rpc GetNearbyObjects(NearbyObjectsRequest) returns (NearbyObjectsResponse);
  rpc StreamClientEvents(stream ClientEvent) returns (stream WorldEvent);
}

service WorldGameLogicService {
  rpc StreamGameEvents(stream GameLogicEvent) returns (stream GameLogicEvent);
}

message CharacterRequest {
  string token = 1;
  string user_id = 2;
  string char_id = 3;
  string session_id = 4;
}

message CharacterResponse {
  int32 count = 1;
}

message CharacterMoveRequest {
  string session_id = 1;
  uint32 target_id = 2;
  float x = 3;
  float y = 4;
  float z = 5;
}

message CharacterMoveResponse {
  int32 id = 1;
  int32 target_id = 2;
  int32 distance = 3;
  float x = 4;
  float y = 5;
  float z = 6;
}

message ChangeMapRequest {
  int32 id = 1;
  float x = 2;
  float y = 3;
}

message ChangeMapResponse {
  int32 id = 1;
  int32 map_id = 2;
  float x = 3;
  float y = 4;
  int32 move_mode = 5;
  int32 ride_mode = 6;
}

message AttackRequest {
  string session_id = 1;
  uint32 target_id = 2;
}

message ObjectHpRequest {
  string session_id = 1;
  uint32 target_id = 2;
}

message ObjectHpResponse {
  uint32 target_id = 1;
  int32 hp = 2;
}

message NearbyObjectsRequest {
  string session_id = 1;
  float x = 2;
  float y = 3;
  float z = 4;
  int32 map_id = 5;
  float radius = 6;
}

message NearbyObjectsResponse {
  repeated WorldObject objects = 1;
}

message WorldObject {
  uint32 id = 1;
  int32 object_type = 2;
  float x = 3;
  float y = 4;
  float z = 5;
  int32 map_id = 6;
  string name = 7;
  int32 hp = 8;
  int32 max_hp = 9;
}

message ClientEvent {
  string session_id = 1;
  string client_id = 2;
  int32 map_id = 3;

  oneof event {
    ClientConnectEvent connect = 4;
    ClientDisconnectEvent disconnect = 5;
    ClientMoveEvent move = 6;
    ClientMapChangeEvent map_change = 7;
  }
}

message ClientConnectEvent {
  float x = 1;
  float y = 2;
  float z = 3;
}

message ClientDisconnectEvent {
  // Empty for now
}

message ClientMoveEvent {
  float x = 1;
  float y = 2;
  float z = 3;
}

message ClientMapChangeEvent {
  int32 old_map_id = 1;
  int32 new_map_id = 2;
  float x = 3;
  float y = 4;
  float z = 5;
}

message WorldEvent {
  repeated string client_ids = 1;

  oneof event {
    NpcSpawnEvent npc_spawn = 2;
    MobSpawnEvent mob_spawn = 3;
    ObjectDespawnEvent object_despawn = 4;
    NearbyObjectsUpdate nearby_update = 5;
  }
}

message GameLogicEvent {
  repeated string client_ids = 1;
  int32 map_id = 2;

  oneof event {
    NpcSpawnEvent npc_spawn = 3;
    MobSpawnEvent mob_spawn = 4;
    ObjectDespawnEvent object_despawn = 5;
    PlayerMoveEvent player_move = 6;
    PlayerConnectEvent player_connect = 7;
    PlayerDisconnectEvent player_disconnect = 8;
  }
}

message NpcSpawnEvent {
  uint32 id = 1;
  float pos_x = 2;
  float pos_y = 3;
  float dest_pos_x = 4;
  float dest_pos_y = 5;
  int32 command = 6;
  uint32 target_id = 7;
  int32 move_mode = 8;
  int32 hp = 9;
  int32 team_id = 10;
  int32 status_flag = 11;
  uint32 npc_id = 12;
  uint32 quest_id = 13;
  float angle = 14;
  int32 event_status = 15;
}

message MobSpawnEvent {
  uint32 id = 1;
  float pos_x = 2;
  float pos_y = 3;
  float dest_pos_x = 4;
  float dest_pos_y = 5;
  int32 command = 6;
  uint32 target_id = 7;
  int32 move_mode = 8;
  int32 hp = 9;
  int32 team_id = 10;
  int32 status_flag = 11;
  uint32 npc_id = 12;
  uint32 quest_id = 13;
}

message ObjectDespawnEvent {
  uint32 object_id = 1;
}

message PlayerMoveEvent {
  string session_id = 1;
  string client_id = 2;
  float x = 3;
  float y = 4;
  float z = 5;
}

message PlayerConnectEvent {
  string session_id = 1;
  string client_id = 2;
  int32 map_id = 3;
  float x = 4;
  float y = 5;
  float z = 6;
}

message PlayerDisconnectEvent {
  string session_id = 1;
  string client_id = 2;
}

message NearbyObjectsUpdate {
  repeated WorldObject objects = 1;
}
