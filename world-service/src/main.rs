mod k8s_orchestrator;
mod world_service;
mod game_logic_client;
mod database_client;

use dotenv::dotenv;
use std::env;
use std::sync::Arc;
use tokio::time::{sleep, Duration, timeout};
use tokio::sync::oneshot;
use tonic::transport::Server;
use utils::service_discovery::{get_kube_service_endpoints_by_dns, get_service_endpoints_by_dns};
use utils::{health_check, logging};
use tracing::{debug, error, info, warn};
use crate::k8s_orchestrator::K8sOrchestrator;
use crate::world_service::{MyWorldService, MyWorldGameLogicService};
use crate::game_logic_client::GameLogicClientManager;
use world_service::world::world_service_server::WorldServiceServer;
use world_service::world::world_game_logic_service_server::WorldGameLogicServiceServer;

fn get_service_name() -> String {
    env::var("WORLD_SERVICE_NAME").unwrap_or_else(|_| "default-service".to_string())
}

async fn get_database_url() -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
    let database_url = format!(
        "http://{}",
        get_kube_service_endpoints_by_dns("database-service", "tcp", "database-service")
            .await?
            .get(0)
            .ok_or("No database service endpoints found")?
    );
    Ok(database_url)
}

fn get_map_ids() -> Vec<u32> {
    // Get the `MAP_IDS` environment variable, such as "42,43,44,45"
    let map_ids_str = env::var("MAP_IDS").unwrap_or_default();
    // Split the string by commas and parse each into a u32. Ignore invalid entries.
    map_ids_str
        .split(',')
        .filter_map(|s| s.trim().parse::<u32>().ok())
        .collect()
}

/// Get connection retry configuration from environment variables with sensible defaults
fn get_connection_retry_config() -> (u32, Duration, Duration) {
    let max_retries = env::var("CONNECTION_INFO_MAX_RETRIES")
        .unwrap_or_else(|_| "3".to_string())
        .parse::<u32>()
        .unwrap_or(3);

    let initial_delay_ms = env::var("CONNECTION_INFO_INITIAL_DELAY_MS")
        .unwrap_or_else(|_| "2000".to_string())
        .parse::<u64>()
        .unwrap_or(2000);

    let max_delay_ms = env::var("CONNECTION_INFO_MAX_DELAY_MS")
        .unwrap_or_else(|_| "10000".to_string())
        .parse::<u64>()
        .unwrap_or(10000);

    (
        max_retries,
        Duration::from_millis(initial_delay_ms),
        Duration::from_millis(max_delay_ms),
    )
}

/// Retry wrapper for getting connection info with exponential backoff
async fn get_connection_info_with_retry(
    orchestrator: &K8sOrchestrator,
    instance_name: &str,
    poll_timeout_secs: u64,
    max_retries: u32,
    initial_delay: Duration,
    max_delay: Duration,
) -> Result<crate::k8s_orchestrator::ConnectionInfo, String> {
    let mut delay = initial_delay;
    let mut last_error_msg = String::new();

    for attempt in 0..=max_retries {
        match orchestrator.poll_connection_info(instance_name, poll_timeout_secs).await {
            Ok(conn_info) => {
                if attempt > 0 {
                    info!("Successfully retrieved connection info for {} instance after {} attempts", instance_name, attempt + 1);
                }
                return Ok(conn_info);
            }
            Err(e) => {
                last_error_msg = e.to_string();
                if attempt < max_retries {
                    warn!("Failed to retrieve connection info for {} instance (attempt {}): {}. Retrying in {:?}...",
                          instance_name, attempt + 1, last_error_msg, delay);
                    sleep(delay).await;
                    delay = std::cmp::min(delay * 2, max_delay);
                } else {
                    error!("Failed to retrieve connection info for {} instance after {} attempts: {}",
                           instance_name, max_retries + 1, last_error_msg);
                    return Err(last_error_msg);
                }
            }
        }
    }

    // This should never be reached due to the loop logic above
    unreachable!()
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    dotenv().ok();
    let app_name = env!("CARGO_PKG_NAME");
    logging::setup_logging(app_name, &["world_service", "health_check"]);
    
    // Get the list of map IDs from the environment variable
    let map_ids = get_map_ids();
    
    // Get the service name from the environment variable
    let service_name = get_service_name();
    
    let instance_names = map_ids.iter().map(|map_id| format!("world-{}-{}", service_name, map_id).to_lowercase()).collect::<Vec<_>>();
    
    // Create a game-logic instance for each map ID we want to manage
    let orchestrator = K8sOrchestrator::new("default").await?;
    let image = "gitea.azgstudio.com/raven/game-logic-service:latest";
    for (map_id, instance_name) in map_ids.iter().zip(instance_names.iter()) {
        match orchestrator.create_game_logic_instance(&instance_name, image, *map_id).await
        {
            Ok(created_pod) => {
                debug!(
                    "Successfully created game-logic instance: {:?}",
                    created_pod.metadata.name,
                );
            }
            Err(e) => {
                if e.to_string().contains("AlreadyExists") {
                    debug!("Game-logic instance already exists: {}", e);
                    // No reason to return an error here.
                    //TODO: We may want to check to make sure the pod is working correctly.
                } else {
                    error!("Error creating game-logic instance: {}", e);
                    return Err(e);
                }
            }
        }
    }
    
    // Get database URL
    let database_url = get_database_url().await?;
    info!("Database service URL: {}", database_url);

    // Create game logic client manager and world service
    let game_logic_manager = Arc::new(GameLogicClientManager::new());
    let world_service_shared = Arc::new(MyWorldService::new_with_game_logic_manager(game_logic_manager.clone(), &database_url).await?);
    let world_service = MyWorldService::new_with_game_logic_manager(game_logic_manager.clone(), &database_url).await?;
    let world_game_logic_service = MyWorldGameLogicService::new(world_service_shared.clone());

    // Get retry configuration for connection info
    let (conn_max_retries, conn_initial_delay, conn_max_delay) = get_connection_retry_config();

    // Connect to game logic instances in parallel using futures::future::join_all
    info!("Connecting to {} game logic instances in parallel...", map_ids.len());

    // Create an Arc to share the orchestrator across tasks
    let orchestrator = Arc::new(orchestrator);

    let connection_futures: Vec<_> = map_ids.iter().zip(instance_names.clone())
        .map(|(map_id, instance_name)| {
            let orchestrator = orchestrator.clone();
            let game_logic_manager = game_logic_manager.clone();
            let map_id = *map_id;
            let instance_name = instance_name.clone();

            async move {
                // Handle the connection info retrieval and convert error to String immediately
                let conn_info = match get_connection_info_with_retry(
                    &orchestrator,
                    &instance_name,
                    30, // poll timeout in seconds (existing behavior)
                    conn_max_retries,
                    conn_initial_delay,
                    conn_max_delay
                ).await {
                    Ok(info) => info,
                    Err(e) => {
                        let error_msg = format!("Map {}: Connection info retrieval failed - {}", map_id, e);
                        error!("Error retrieving connection info for {} instance after retries: {}", instance_name, e);
                        warn!("Skipping map {} due to connection info retrieval failure", map_id);
                        return Err(error_msg);
                    }
                };

                debug!("Successfully retrieved connection info for {} instance: {:?}", instance_name, conn_info);
                let endpoint = format!("http://{}:{}", conn_info.ip, conn_info.port);
                info!("Attempting to connect to game logic service for map {} at endpoint: {}", map_id, endpoint);

                // Try to resolve the DNS name to see if it's reachable
                match tokio::net::lookup_host(&format!("{}:{}", conn_info.ip, conn_info.port)).await {
                    Ok(addrs) => {
                        let resolved_addrs: Vec<_> = addrs.collect();
                        info!("DNS resolution successful for {}: {:?}", conn_info.ip, resolved_addrs);
                    }
                    Err(e) => {
                        warn!("DNS resolution failed for {}: {}", conn_info.ip, e);
                    }
                }

                // Give the service a moment to be ready
                tokio::time::sleep(tokio::time::Duration::from_secs(2)).await;

                // Try alternative endpoint formats if the full DNS name doesn't work
                let alternative_endpoints = vec![
                    // endpoint.clone(), // this one doesn't seem to work
                    format!("http://{}-service:{}", instance_name, conn_info.port), // Short service name
                    format!("http://{}:{}", conn_info.ip.split('.').next().unwrap_or(&conn_info.ip), conn_info.port), // Just service name without domain
                ];

                let mut connection_successful = false;
                let mut last_connection_error = String::new();

                // Try each endpoint format
                for (i, test_endpoint) in alternative_endpoints.iter().enumerate() {
                    debug!("Trying endpoint format {}: {}", i + 1, test_endpoint);
                    match game_logic_manager.add_client(map_id, test_endpoint.clone()).await {
                        Ok(()) => {
                            info!("Successfully connected to game logic service for map {} using endpoint: {}", map_id, test_endpoint);
                            connection_successful = true;
                            break;
                        }
                        Err(e) => {
                            debug!("Failed to connect using endpoint {}: {}", test_endpoint, e);
                            last_connection_error = format!("Endpoint {}: {}", test_endpoint, e);
                            if i < alternative_endpoints.len() - 1 {
                                debug!("Trying next endpoint format...");
                            }
                        }
                    }
                }

                if connection_successful {
                    Ok(map_id)
                } else {
                    error!("Failed to connect to game logic service for map {} using any endpoint format. Last error: {}", map_id, last_connection_error);
                    Err(format!("Map {}: Failed to connect using any endpoint format - {}", map_id, last_connection_error))
                }
            }
        })
        .collect();

    // Wait for all connection attempts to complete
    let connection_results = futures::future::join_all(connection_futures).await;

    // Process connection results
    let mut successful_connections = 0;
    let mut failed_connections = Vec::new();

    for result in connection_results {
        match result {
            Ok(map_id) => {
                successful_connections += 1;
                debug!("Connection for map {} completed successfully", map_id);
            }
            Err(error_msg) => {
                failed_connections.push(error_msg);
            }
        }
    }

    info!("Game logic connection summary: {} successful, {} failed",
          successful_connections, failed_connections.len());

    if !failed_connections.is_empty() {
        warn!("Failed connections: {:?}", failed_connections);
    }

    if successful_connections == 0 {
        error!("No game logic instances could be connected! Service may not function properly.");
    } else {
        info!("World service startup completed with {} active game logic connections", successful_connections);
    }

    // Set the gRPC server address
    let addr = env::var("LISTEN_ADDR").unwrap_or_else(|_| "0.0.0.0".to_string());
    let port = env::var("SERVICE_PORT").unwrap_or_else(|_| "50054".to_string());
    // let db_url = format!(
    //     "http://{}",
    //     get_kube_service_endpoints_by_dns("database-service", "tcp", "database-service")
    //         .await?
    //         .get(0)
    //         .unwrap()
    // );
    // let chat_service = format!(
    //     "http://{}",
    //     get_kube_service_endpoints_by_dns("chat-service", "tcp", "chat-service")
    //         .await?
    //         .get(0)
    //         .unwrap()
    // );

    // Start gRPC server with graceful shutdown support
    let grpc_addr = format!("{}:{}", addr, port).parse()?;
    info!("Starting World Service gRPC server on {}", grpc_addr);

    // Create shutdown signal channel
    let (shutdown_tx, shutdown_rx) = oneshot::channel::<()>();

    let server_task = tokio::spawn(async move {
        let server = Server::builder()
            .add_service(WorldServiceServer::new(world_service))
            .add_service(WorldGameLogicServiceServer::new(world_game_logic_service))
            .serve_with_shutdown(grpc_addr, async {
                shutdown_rx.await.ok();
                debug!("gRPC server shutdown signal received");
            });

        if let Err(e) = server.await {
            error!("gRPC server error: {}", e);
        } else {
            debug!("gRPC server shut down gracefully");
        }
    });

    // Register service with Consul
    health_check::start_health_check(addr.as_str()).await?;

    // Wait for shutdown signal
    info!("World service is running. Waiting for shutdown signal...");
    utils::signal_handler::wait_for_signal().await;

    info!("Shutdown signal received. Beginning graceful shutdown...");

    // Step 1: Signal the gRPC server to stop accepting new connections
    if let Err(_) = shutdown_tx.send(()) {
        warn!("Failed to send shutdown signal to gRPC server (receiver may have been dropped)");
    }

    // Step 2: Wait for the gRPC server to finish with a timeout
    match timeout(Duration::from_secs(30), server_task).await {
        Ok(result) => {
            if let Err(e) = result {
                error!("gRPC server task failed: {}", e);
            }
        }
        Err(_) => {
            error!("gRPC server shutdown timed out after 30 seconds");
        }
    }

    // Step 3: Shutdown all game-logic instances
    info!("Shutting down {} game-logic instances...", instance_names.len());
    let mut shutdown_errors = Vec::new();

    for instance_name in &instance_names {
        match timeout(Duration::from_secs(10), orchestrator.shutdown_instance(instance_name)).await {
            Ok(Ok(())) => {
                info!("Successfully shut down game-logic instance: {}", instance_name);
            }
            Ok(Err(e)) => {
                error!("Failed to shutdown game-logic instance {}: {}", instance_name, e);
                shutdown_errors.push(format!("Instance {}: {}", instance_name, e));
            }
            Err(_) => {
                error!("Timeout shutting down game-logic instance: {}", instance_name);
                shutdown_errors.push(format!("Instance {}: timeout", instance_name));
            }
        }
    }

    if shutdown_errors.is_empty() {
        info!("All components shut down successfully");
    } else {
        warn!("Some components failed to shut down cleanly: {:?}", shutdown_errors);
    }

    Ok(())
}
