use crate::connection_state::ConnectionState;
use crate::database_client::{DatabaseClient, generate_client_id};
use crate::id_manager::IdManager;
use dashmap::DashMap;
use std::sync::{Arc, Mutex};
use tokio::net::TcpStream;
use tokio::io::{WriteHalf};
use tracing::{debug, error, info, warn};

#[derive(Clone)]
pub struct ConnectionService {
    pub connections: Arc<DashMap<String, ConnectionState>>, // Map connection ID to state
    pub id_manager: Arc<Mutex<IdManager>>,
    pub database_client: Arc<Mutex<DatabaseClient>>,
}

impl ConnectionService {
    pub async fn new(database_url: &str) -> Result<Self, Box<dyn std::error::Error + Send + Sync>> {
        let database_client = DatabaseClient::new(database_url).await?;

        Ok(Self {
            connections: Arc::new(DashMap::new()),
            id_manager: Arc::new(Mutex::new(IdManager::new())),
            database_client: Arc::new(Mutex::new(database_client)),
        })
    }

    pub async fn add_connection(&self, writer: Arc<tokio::sync::Mutex<WriteHalf<TcpStream>>>, peer_addr: String) -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
        let client_id = generate_client_id();

        // Store client mapping in database
        let connection_info = serde_json::json!({
            "peer_addr": peer_addr,
            "connected_at": chrono::Utc::now().to_rfc3339()
        }).to_string();

        match self.database_client.lock().await.store_client(&client_id, &connection_info).await {
            Ok(true) => {
                info!("Successfully stored client mapping for: {}", client_id);

                let mut connection_state = ConnectionState::new();
                connection_state.writer = Some(writer);
                connection_state.client_id = Some(client_id.clone());
                self.connections.insert(client_id.clone(), connection_state);

                Ok(client_id)
            }
            Ok(false) => {
                error!("Failed to store client mapping for: {}", client_id);
                Err("Failed to store client mapping".into())
            }
            Err(e) => {
                error!("Database error storing client mapping: {}", e);
                Err(e)
            }
        }
    }

    pub fn get_connection(&self, connection_id: &str) -> Option<ConnectionState> {
        self.connections.get(connection_id).map(|entry| entry.clone())
    }

    pub fn get_connection_mut(
        &self,
        connection_id: &str,
    ) -> Option<dashmap::mapref::one::RefMut<'_, String, ConnectionState>> {
        self.connections.get_mut(connection_id)
    }

    pub fn remove_connection(&self, connection_id: &str) {
        self.connections.remove(connection_id);
    }

    pub fn next_id(&self) -> u16 {
        let mut manager = self.id_manager.lock().unwrap();
        manager.get_free_id()
    }

    pub fn free_id(&self, id: u16) {
        let mut manager = self.id_manager.lock().unwrap();
        manager.release_id(id);
    }
}
